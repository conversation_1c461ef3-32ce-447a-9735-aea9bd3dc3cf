# 规格代理工作流 - 使用指南和示例

## 概述

规格代理工作流系统是一个全面的AI驱动开发管道，通过专业代理在协调阶段工作，将项目想法转化为生产就绪的代码。本指南提供实用示例和使用说明。

## 快速开始

### 基本使用

```bash
# 为新项目启动完整工作流
使用 spec-orchestrator 代理并说：创建一个带用户认证的待办事项Web应用

# 从现有需求开始
使用 spec-orchestrator 代理：--from-requirements ./docs/requirements.md

# 仅执行特定阶段
使用 spec-orchestrator 代理：--phase development --from-artifacts ./planning/
```

## 代理目录结构

```
.claude/agents/
├── spec-agents/
│   ├── spec-orchestrator.md    # 工作流协调器
│   ├── spec-analyst.md         # 需求分析
│   ├── spec-architect.md       # 系统设计
│   ├── spec-planner.md         # 任务规划
│   ├── spec-developer.md       # 代码实现
│   ├── spec-tester.md          # 测试
│   ├── spec-reviewer.md        # 代码审查
│   └── spec-validator.md       # 最终验证
├── ui-ux-master-agent.md       # UI/UX设计集成
├── senior-backend-architect.md  # 后端专业知识
└── senior-frontend-architect.md # 前端专业知识
```

## 工作流示例

### 示例1：简单Web应用

```markdown
**项目**: 个人博客平台

**输入到 spec-orchestrator**:
创建一个个人博客平台，支持markdown、用户评论和管理面板

**工作流执行**:

1. **规划阶段** (45分钟)
   - spec-analyst 创建 requirements.md
   - spec-architect 设计 architecture.md
   - spec-planner 生成 tasks.md
   - 质量门控1: 通过 (96/100)

2. **开发阶段** (2小时)
   - spec-developer 实现15个任务
   - spec-tester 编写全面测试
   - 质量门控2: 通过 (88/100)

3. **验证阶段** (30分钟)
   - spec-reviewer 执行代码审查
   - spec-validator 最终检查
   - 质量门控3: 通过 (91/100)

**输出**: 完整博客平台包含:
- 带markdown编辑器的React前端
- Node.js/Express后端
- PostgreSQL数据库
- 85%测试覆盖率
- 完整文档
```

### 示例2：企业系统

```markdown
**项目**: 多租户SaaS CRM

**输入到 spec-orchestrator**:
--quality-threshold 95 --verbose
构建企业CRM系统，包含多租户、基于角色的访问控制、
API集成和实时分析仪表板

**工作流执行**:

1. **规划阶段** (2小时)
   - 包含47个用户故事的详细需求
   - 微服务架构设计
   - 156个实现任务
   - 质量门控1: 通过 (98/100)

2. **开发阶段** (8小时)
   - 实现6个微服务
   - GraphQL API网关
   - 带D3.js的React仪表板
   - 质量门控2: 条件通过 (84/100)
   - 反馈循环: 需要性能优化

3. **修订周期** (2小时)
   - spec-developer 优化数据库查询
   - spec-reviewer 建议缓存策略
   - 质量门控2: 通过 (95/100)

4. **验证阶段** (1小时)
   - 全面安全审计
   - 性能基准验证
   - 质量门控3: 通过 (96/100)
```

### 示例3：移动优先电商

```markdown
**项目**: 电商移动应用后端

**与UI/UX大师的协作示例**:

spec-orchestrator 与 ui-ux-master 协调设计规格

**阶段1**: UI/UX设计 (与ui-ux-master)
- 用户旅程映射
- 移动优先组件设计
- 设计系统创建

**阶段2**: 后端架构 (与senior-backend-architect)
- 移动优化的API设计
- 可扩展性微服务
- Redis缓存策略

**阶段3**: 前端集成 (与senior-frontend-architect)
- React Native实现
- 离线优先架构
- 性能优化

**结果**: 完整电商平台包含:
- 3G网络下2秒内页面加载
- 99.9% API正常运行时间
- WCAG AA合规性
- 92%质量评分
```

## 命令参考

### 编排器命令

```bash
# 带选项的完整工作流
spec-orchestrator: 创建 [项目描述]
  --quality-threshold 90      # 设置最低质量评分 (默认: 85)
  --skip-agents analyst      # 跳过特定代理
  --verbose                  # 详细进度输出
  --parallel                 # 启用并行任务执行

# 阶段特定执行
spec-orchestrator: --phase planning "创建任务跟踪器"
spec-orchestrator: --phase development --from-artifacts ./planning/
spec-orchestrator: --phase validation --project-path ./my-project/

# 工作流控制
spec-orchestrator: --status              # 检查当前工作流状态
spec-orchestrator: --pause               # 暂停当前工作流
spec-orchestrator: --resume              # 恢复暂停的工作流
spec-orchestrator: --abort               # 取消当前工作流
```

### 单个代理使用

```bash
# 直接代理调用 (不使用编排器时)
spec-analyst: 分析社交媒体仪表板的需求
spec-architect: 从 ./requirements.md 设计架构
spec-planner: 从 ./architecture.md 创建任务分解
spec-developer: 从 ./tasks.md 实现 TASK-001
spec-tester: 为 ./src/auth/ 编写测试
spec-reviewer: 审查 ./src/ 中的代码
spec-validator: 验证 ./my-app/ 中的项目
```

## 质量门控说明

### 门控1: 规划质量 (spec-planner之后)

```yaml
标准:
  - 需求完整性 ≥ 95%
  - 架构可行性已验证
  - 所有用户故事都有验收标准
  - 任务分解是全面的
  
失败行动:
  - 返回spec-analyst进行澄清
  - 对缺失元素的具体反馈
```

### 门控2: 开发质量 (spec-tester之后)

```yaml
标准:
  - 所有测试通过
  - 代码覆盖率 ≥ 80%
  - 无关键安全漏洞
  - 满足性能基准
  
失败行动:
  - spec-developer修复已识别问题
  - spec-tester重新运行失败测试
```

### 门控3: 生产就绪性 (spec-validator之后)

```yaml
标准:
  - 整体质量评分 ≥ 85%
  - 所有需求已实现
  - 文档完整
  - 部署脚本已测试
  
失败行动:
  - 路由到适当代理进行修复
  - 可能需要规划或开发修订
```

## 与现有代理的集成

### UI/UX协作

```markdown
**场景**: 构建设计重点应用

1. ui-ux-master 创建设计规格
2. spec-orchestrator 摄取设计规格
3. spec-analyst 纳入UI需求
4. spec-architect 确保设计系统集成
5. spec-developer 使用设计令牌实现
6. spec-reviewer 验证设计合规性
```

### 后端架构集成

```markdown
**场景**: 复杂分布式系统

1. senior-backend-architect 提供系统设计模式
2. spec-architect 纳入分布式模式
3. spec-planner 创建微服务特定任务
4. spec-developer 使用适当模式实现
5. spec-tester 编写集成测试
6. senior-backend-architect 审查关键组件
```

### 前端架构集成

```markdown
**场景**: 带SSR的现代SPA

1. senior-frontend-architect 定义组件架构
2. spec-architect 纳入前端模式
3. spec-planner 按组件分解
4. spec-developer 使用React/Next.js实现
5. spec-tester 编写组件测试
6. senior-frontend-architect 审查性能
```

## 常见模式和解决方案

### 模式1: 快速原型

```bash
# 为MVP跳过全面规划
spec-orchestrator: --skip-agents analyst --quality-threshold 75
创建一个带邮箱捕获的简单着陆页

# 结果更快但不够全面的输出
```

### 模式2: 高安全应用

```bash
# 在整个工作流中强调安全
spec-orchestrator: --quality-threshold 95 --focus security
创建一个带欺诈检测的银行交易系统

# 在每个阶段触发额外安全检查
```

### 模式3: 性能关键系统

```bash
# 专注于性能优化
spec-orchestrator: --focus performance --verbose
创建一个<10ms延迟的实时交易平台

# 添加性能基准和优化周期
```

### 模式4: 遗留现代化

```bash
# 使用现有代码库
spec-orchestrator: --phase analysis --existing-code ./legacy/
将遗留PHP应用现代化为微服务

# 在规划迁移前分析现有代码
```

## 故障排除

### 常见问题

1. **质量门控失败**
   - 检查失败的具体标准
   - 审查提供给代理的反馈
   - 允许代理修订其工作
   - 如适当考虑调整阈值

2. **代理协调问题**
   - 确保所有代理都正确安装
   - 检查文件路径冲突
   - 验证工件命名约定
   - 审查编排器日志

3. **性能问题**
   - 启用并行执行
   - 跳过非关键代理
   - 为原型降低质量阈值
   - 使用阶段特定执行

### 调试模式

```bash
# 启用详细调试
spec-orchestrator: --debug --log-level verbose
创建测试项目

# 输出详细日志用于故障排除
```

## 最佳实践

### 1. **项目准备**

- 有清晰的项目描述
- 收集任何现有文档
- 预先定义成功标准
- 设置适当的质量阈值

### 2. **工作流优化**

- 为大型项目使用并行执行
- 适当时跳过代理
- 为迭代开发缓存结果
- 监控资源使用

### 3. **质量管理**

- 没有充分理由不要降低阈值
- 立即解决质量问题
- 有效使用反馈循环
- 跟踪质量趋势

### 4. **协作**

- 需要时集成专业代理
- 维护清晰的沟通渠道
- 记录决策和变更
- 在团队成员间共享工件

记住：规格代理工作流系统代表AI辅助开发的范式转变。通过利用协同工作的专业代理，您可以实现更快的开发周期、更高的代码质量和全面的文档——所有这些都只需最少的人工干预。

欢迎来到AI驱动开发的未来！
